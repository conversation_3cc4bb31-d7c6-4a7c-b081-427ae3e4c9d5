package eu.torvian.chatbot.server.service.core.impl

import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.raise.either
import arrow.core.raise.withError
import arrow.core.right
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.server.data.dao.MessageDao
import eu.torvian.chatbot.server.data.dao.SessionDao
import eu.torvian.chatbot.server.data.dao.error.InsertMessageError
import eu.torvian.chatbot.server.data.dao.error.MessageAddChildError
import eu.torvian.chatbot.server.data.dao.error.MessageError
import eu.torvian.chatbot.server.data.dao.error.SessionError
import eu.torvian.chatbot.server.service.core.LLMModelService
import eu.torvian.chatbot.server.service.core.LLMProviderService
import eu.torvian.chatbot.server.service.core.MessageService
import eu.torvian.chatbot.server.service.core.ModelSettingsService
import eu.torvian.chatbot.server.service.core.error.message.DeleteMessageError
import eu.torvian.chatbot.server.service.core.error.message.ProcessNewMessageError
import eu.torvian.chatbot.server.service.core.error.message.UpdateMessageContentError
import eu.torvian.chatbot.server.service.core.error.message.toApiError
import eu.torvian.chatbot.server.service.core.error.model.GetModelError
import eu.torvian.chatbot.server.service.core.error.provider.GetProviderError
import eu.torvian.chatbot.server.service.core.error.settings.GetSettingsByIdError
import eu.torvian.chatbot.server.service.llm.LLMApiClient
import eu.torvian.chatbot.server.service.llm.LLMCompletionError
import eu.torvian.chatbot.server.service.llm.LLMCompletionResult
import eu.torvian.chatbot.server.service.llm.LLMStreamChunk
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.channelFlow
import kotlinx.datetime.Clock
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger

/**
 * Data class to hold LLM configuration components.
 */
private data class LlmConfig(
    val model: eu.torvian.chatbot.common.models.LLMModel,
    val settings: eu.torvian.chatbot.common.models.ModelSettings,
    val provider: eu.torvian.chatbot.common.models.LLMProvider,
    val apiKey: String?
)

/**
 * Implementation of the [MessageService] interface.
 * Orchestrates message persistence, threading, and LLM interaction.
 */
class MessageServiceImpl(
    private val messageDao: MessageDao,
    private val sessionDao: SessionDao,
    private val llmModelService: LLMModelService,
    private val modelSettingsService: ModelSettingsService,
    private val llmProviderService: LLMProviderService,
    private val llmApiClient: LLMApiClient,
    private val credentialManager: CredentialManager,
    private val transactionScope: TransactionScope,
) : MessageService {

    companion object {
        private val logger: Logger = LogManager.getLogger(MessageServiceImpl::class.java)
    }

    override suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage> {
        return transactionScope.transaction {
            messageDao.getMessagesBySessionId(sessionId)
        }
    }

    override suspend fun processNewMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Either<ProcessNewMessageError, List<ChatMessage>> =
        transactionScope.transaction {
            either {

                // 1. Validate session
                val session = withError({ daoError: SessionError.SessionNotFound ->
                    ProcessNewMessageError.SessionNotFound(daoError.id)
                }) {
                    sessionDao.getSessionById(sessionId).bind()
                }

                // 2. Save user message
                val userMessage = withError({ daoError: InsertMessageError ->
                    when (daoError) {
                        is InsertMessageError.SessionNotFound -> {
                            throw IllegalStateException("Session $sessionId not found after validation")
                        }

                        is InsertMessageError.ParentNotInSession -> {
                            ProcessNewMessageError.ParentNotInSession(daoError.sessionId, daoError.parentId)
                        }
                    }
                }) {
                    messageDao.insertUserMessage(sessionId, content, parentMessageId).bind()
                }

                // 3. Add user message as child to parent
                if (parentMessageId != null) {
                    withError({ daoError: MessageAddChildError ->
                        throw IllegalStateException("Failed to add user message as child to parent: ${daoError.javaClass.simpleName}")
                    }) {
                        messageDao.addChildToMessage(parentMessageId, userMessage.id).bind()
                    }
                }

                // 4. Get model and settings config
                val modelId = session.currentModelId
                    ?: raise(ProcessNewMessageError.ModelConfigurationError("No model selected for session"))
                val settingsId = session.currentSettingsId
                    ?: raise(ProcessNewMessageError.ModelConfigurationError("No settings selected for session"))

                // Fetch Model
                val model = withError({ serviceError: GetModelError ->
                    throw IllegalStateException("Model with ID $modelId not found after validation")
                }) {
                    llmModelService.getModelById(modelId).bind()
                }

                // Fetch Settings
                val settings = withError({ serviceError: GetSettingsByIdError ->
                    when (serviceError) {
                        is GetSettingsByIdError.SettingsNotFound -> {
                            throw IllegalStateException("Settings with ID $settingsId not found after validation")
                        }
                    }
                }) {
                    modelSettingsService.getSettingsById(settingsId).bind()
                }

                // Get provider and API Key from the model's provider (if the provider requires one)
                val (provider, apiKey) = run {
                    // First get the provider for this model
                    val provider = withError({ serviceError: GetProviderError ->
                        throw IllegalStateException("Provider not found for model ID $modelId (provider ID: ${model.providerId})")
                    }) {
                        llmProviderService.getProviderById(model.providerId).bind()
                    }

                    // Then get the credential using the provider's API key ID (if it has one)
                    val apiKey = provider.apiKeyId?.let { keyId ->
                        withError({ credError: CredentialError.CredentialNotFound ->
                            throw IllegalStateException("API key not found in secure storage for provider ID ${provider.id} (key alias: $keyId)")
                        }) {
                            credentialManager.getCredential(keyId).bind()
                        }
                    }

                    provider to apiKey
                }

                // 5. Build context
                val allMessagesInSession = messageDao.getMessagesBySessionId(sessionId)
                val context = buildContext(userMessage, allMessagesInSession)

                // 6. Call LLM API
                val llmResponseResult = withError({ llmError: LLMCompletionError ->
                    logger.error("LLM API call failed for session $sessionId, provider ${provider.name}: $llmError")
                    ProcessNewMessageError.ExternalServiceError(llmError)
                }) {
                    llmApiClient.completeChat(
                        messages = context,
                        modelConfig = model,
                        provider = provider,
                        settings = settings,
                        apiKey = apiKey
                    ).bind()
                }
                logger.info("LLM API call successful for session $sessionId")

                // 7. Process the LLM response and save the assistant message.
                // Extract content from the first completion choice.
                val assistantMessageContent =
                    llmResponseResult.choices.firstOrNull()?.content ?: run {
                        logger.error("LLM API returned successful response with no choices or empty content for session $sessionId")
                        // Treat an empty response as an error scenario
                        raise(ProcessNewMessageError.ExternalServiceError(LLMCompletionError.InvalidResponseError("LLM API returned success but no completion choices.")))
                    }
                val assistantMessage = withError({ daoError: InsertMessageError ->
                    throw IllegalStateException("Failed to insert assistant message: ${daoError.javaClass.simpleName}")
                }) {
                    messageDao.insertAssistantMessage(
                        sessionId,
                        assistantMessageContent,
                        userMessage.id,
                        modelId,
                        settingsId
                    ).bind()
                }

                // 8. Add assistant message as child to user message
                withError({ daoError: MessageAddChildError ->
                    throw IllegalStateException("Failed to add assistant message as child to user message: ${daoError.javaClass.simpleName}")
                }) {
                    messageDao.addChildToMessage(userMessage.id, assistantMessage.id).bind()
                }

                // 9. Update session's leaf message ID
                withError({ daoError: SessionError ->
                    throw IllegalStateException("Failed to update session leaf message ID: ${daoError.javaClass.simpleName}")
                }) {
                    sessionDao.updateSessionLeafMessageId(sessionId, assistantMessage.id).bind()
                }

                // 10. Return new messages as the success value
                listOf(userMessage, assistantMessage)
            }
        }

    override fun processNewMessageStreaming(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Flow<Either<ProcessNewMessageError, ChatUpdate>> = channelFlow {
        // 1. Save user message immediately in its own transaction block.
        // This ensures the user message is persisted even if streaming fails later.
        val userMessage = transactionScope.transaction {
            either {
                val session = sessionDao.getSessionById(sessionId)
                    .getOrElse { raise(ProcessNewMessageError.SessionNotFound(sessionId)) }
                val userMsg = messageDao.insertUserMessage(sessionId, content, parentMessageId).mapLeft { daoError ->
                    when (daoError) {
                        is InsertMessageError.ParentNotInSession -> ProcessNewMessageError.ParentNotInSession(
                            daoError.sessionId,
                            daoError.parentId
                        )

                        is InsertMessageError.SessionNotFound -> ProcessNewMessageError.SessionNotFound(daoError.sessionId)
                    }
                }.bind()
                if (parentMessageId != null) {
                    messageDao.addChildToMessage(parentMessageId, userMsg.id).mapLeft { linkError ->
                        // Log a warning, but don't fail the whole operation for a linking issue
                        logger.warn("Failed to link user message ${userMsg.id} as child to parent ${parentMessageId}: ${linkError::class.simpleName}")
                        ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link user message.")
                    }.bind()
                }
                userMsg
            }
        }.fold(
            ifLeft = { error ->
                logger.error("Initial user message save failed for session $sessionId: $error")
                send(error.left()) // Send error to client and terminate flow
                close()
                return@channelFlow
            },
            ifRight = { it }
        )

        // 2. Emit the successfully saved user message as the first update
        send(ChatUpdate.UserMessageUpdate(userMessage).right())

        // 3. Get LLM model, settings, provider, and API key
        val llmConfigResult = transactionScope.transaction {
            val session = sessionDao.getSessionById(sessionId).getOrElse {
                return@transaction ProcessNewMessageError.SessionNotFound(sessionId).left()
            }
            getLlmConfig(session, session.currentModelId ?: -1, session.currentSettingsId ?: -1)
        }.getOrElse { error ->
            logger.error("LLM configuration retrieval failed for session $sessionId: $error")
            send(error.left()) // Send error to client and terminate flow
            close()
            return@channelFlow
        }
        val model = llmConfigResult.model
        val settings = llmConfigResult.settings
        val provider = llmConfigResult.provider
        val apiKey = llmConfigResult.apiKey

        // 4. Build context for LLM call (includes the newly saved user message)
        val allMessagesInSession = transactionScope.transaction { messageDao.getMessagesBySessionId(sessionId) }
        val context = buildContext(userMessage, allMessagesInSession)

        // Generate a temporary ID for the assistant message on the server-side,
        // which will be passed to the client to track the streaming message.
        // The client will then replace this temporary ID with the real DB ID at the end.
        val temporaryAssistantMessageId = -1L // A common convention for "not-yet-persisted" or client-only ID

        // 5. Emit AssistantMessageStart with temporary assistant message to client
        val temporaryAssistantMessage = ChatMessage.AssistantMessage(
            id = temporaryAssistantMessageId,
            sessionId = sessionId,
            content = "",
            parentMessageId = userMessage.id,
            childrenMessageIds = emptyList(),
            createdAt = Clock.System.now(),
            updatedAt = Clock.System.now(),
            modelId = model.id,
            settingsId = settings.id
        )
        send(
            ChatUpdate.AssistantMessageStart(
                assistantMessage = temporaryAssistantMessage
            ).right()
        )

        var accumulatedContent = ""
        var finalUsage: LLMCompletionResult.UsageStats = LLMCompletionResult.UsageStats(0, 0, 0)
        var finalFinishReason: String? = null

        try {
            // 6. Call LLM API in streaming mode and collect chunks
            llmApiClient.completeChatStreaming(context, model, provider, settings, apiKey)
                .collect { llmStreamChunkEither ->
                    llmStreamChunkEither.fold(
                        ifLeft = { llmError ->
                            logger.error("LLM API streaming error for session $sessionId, provider ${provider.name}: $llmError")
                            // If an error occurs in the stream, emit it and signal termination
                            send(
                                ChatUpdate.ErrorUpdate(
                                    ProcessNewMessageError.ExternalServiceError(llmError).toApiError()
                                ).right()
                            )
                            throw RuntimeException("LLM API streaming error: $llmError") // Propagate error to cause catch block
                        },
                        ifRight = { chunk ->
                            when (chunk) {
                                is LLMStreamChunk.ContentChunk -> {
                                    accumulatedContent += chunk.deltaContent
                                    finalFinishReason = chunk.finishReason
                                        ?: finalFinishReason // Update finish reason if provided mid-stream
                                    send(
                                        ChatUpdate.AssistantMessageDelta(
                                            temporaryAssistantMessageId,
                                            chunk.deltaContent
                                        ).right()
                                    )
                                }

                                is LLMStreamChunk.UsageChunk -> {
                                    finalUsage = LLMCompletionResult.UsageStats(
                                        chunk.promptTokens,
                                        chunk.completionTokens,
                                        chunk.totalTokens
                                    )
                                }

                                LLMStreamChunk.Done -> {
                                    // 7. Stream is finished and successful. NOW save the assistant message to the DB.
                                    val (savedAssistantMessage, updatedUserMessage) = transactionScope.transaction {
                                        either {
                                            val assistantMsg = messageDao.insertAssistantMessage(
                                                sessionId,
                                                accumulatedContent,
                                                userMessage.id, // Parent is user message
                                                model.id,
                                                settings.id
                                            ).mapLeft { daoError ->
                                                ProcessNewMessageError.ModelConfigurationError("Failed to insert final assistant message: ${daoError::class.simpleName}")
                                            }.bind()

                                            // Link assistant message as child to user message in DB
                                            messageDao.addChildToMessage(userMessage.id, assistantMsg.id)
                                                .mapLeft { linkError ->
                                                    logger.warn("Failed to link assistant message ${assistantMsg.id} as child to user message ${userMessage.id}: ${linkError::class.simpleName}")
                                                    ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link assistant message.")
                                                }.bind()

                                            // Update session's leaf message ID to the newly saved assistant message
                                            sessionDao.updateSessionLeafMessageId(sessionId, assistantMsg.id)
                                                .mapLeft { updateError ->
                                                    logger.warn("Failed to update session leaf message ID to ${assistantMsg.id}: ${updateError::class.simpleName}")
                                                    ProcessNewMessageError.ModelConfigurationError("Internal error: failed to update session leaf.")
                                                }.bind()

                                            // Get the updated user message with the new child
                                            val updatedUserMsg = messageDao.getMessageById(userMessage.id).mapLeft { daoError ->
                                                ProcessNewMessageError.ModelConfigurationError("Failed to retrieve updated user message: ${daoError::class.simpleName}")
                                            }.bind() as ChatMessage.UserMessage

                                            assistantMsg to updatedUserMsg
                                        }
                                    }.getOrElse { error ->
                                        logger.error("Final assistant message save failed for session $sessionId: $error")
                                        send(
                                            ChatUpdate.ErrorUpdate(
                                                ProcessNewMessageError.ExternalServiceError(
                                                    LLMCompletionError.OtherError("Failed to save final message.")
                                                ).toApiError()
                                            ).right()
                                        )
                                        throw RuntimeException("Failed to save final message: $error") // Propagate error to outer catch
                                    }

                                    // 8. Emit the final update with the REAL database ID and updated user message
                                    send(
                                        ChatUpdate.AssistantMessageEnd(
                                            tempMessageId = temporaryAssistantMessageId,
                                            finalAssistantMessage = savedAssistantMessage,
                                            finalUserMessage = updatedUserMessage
                                        ).right()
                                    )
                                    send(ChatUpdate.Done.right()) // Signal flow completion
                                }

                                is LLMStreamChunk.Error -> {
                                    logger.error("LLM API returned streaming error chunk: ${chunk.llmError}")
                                    send(
                                        ChatUpdate.ErrorUpdate(
                                            ProcessNewMessageError.ExternalServiceError(chunk.llmError).toApiError()
                                        ).right()
                                    )
                                    throw RuntimeException("LLM streaming error: ${chunk.llmError}") // Propagate error
                                }
                            }
                        }
                    )
                }
        } catch (e: Exception) {
            // This catches any errors that were `throw`n (including those from LLM API stream)
            logger.error(
                "Streaming message processing failed for session $sessionId due to unhandled exception: ${e.message}",
                e
            )
            send(
                ChatUpdate.ErrorUpdate(
                    ProcessNewMessageError.ExternalServiceError(
                        LLMCompletionError.OtherError(
                            "An unexpected error occurred during streaming.",
                            e
                        )
                    ).toApiError()
                ).right()
            )
        } finally {
            close() // Ensure the channel is closed after completion or error
        }
    }

    private suspend fun getLlmConfig(
        session: ChatSession,
        modelId: Long,
        settingsId: Long
    ): Either<ProcessNewMessageError, LlmConfig> = either {
        val model = llmModelService.getModelById(modelId).mapLeft { serviceError ->
            ProcessNewMessageError.ModelConfigurationError("Model with ID $modelId not found: ${serviceError::class.simpleName}")
        }.bind()

        val settings = modelSettingsService.getSettingsById(settingsId).mapLeft { serviceError ->
            ProcessNewMessageError.ModelConfigurationError("Settings with ID $settingsId not found: ${serviceError::class.simpleName}")
        }.bind()

        val provider = llmProviderService.getProviderById(model.providerId).mapLeft { serviceError ->
            ProcessNewMessageError.ModelConfigurationError("Provider not found for model ID $modelId (provider ID: ${model.providerId}): ${serviceError::class.simpleName}")
        }.bind()

        val apiKey = provider.apiKeyId?.let { keyId ->
            credentialManager.getCredential(keyId).mapLeft { credError ->
                ProcessNewMessageError.ModelConfigurationError("API key not found in secure storage for provider ID ${provider.id} (key alias: $keyId)")
            }.bind()
        }
        LlmConfig(model, settings, provider, apiKey)
    }

    override suspend fun updateMessageContent(
        id: Long,
        content: String
    ): Either<UpdateMessageContentError, ChatMessage> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    UpdateMessageContentError.MessageNotFound(daoError.id)
                }) {
                    messageDao.updateMessageContent(id, content).bind()
                }
            }
        }

    override suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    DeleteMessageError.MessageNotFound(daoError.id)
                }) {
                    messageDao.deleteMessage(id).bind()
                }
            }
        }

    /**
     * Builds the context for the LLM API call.
     *
     * @param currentUserMessage The user's message.
     * @param allMessages All messages in the session.
     * @return The context as a list of [ChatMessage] objects.
     */
    private fun buildContext(currentUserMessage: ChatMessage, allMessages: List<ChatMessage>): List<ChatMessage> {
        val context = mutableListOf<ChatMessage>()
        val messageMap = allMessages.associateBy { it.id }
        var c: ChatMessage? = currentUserMessage
        while (c != null) {
            context.add(0, c)
            c = c.parentMessageId?.let { messageMap[it] }
        }
        return context
    }
}