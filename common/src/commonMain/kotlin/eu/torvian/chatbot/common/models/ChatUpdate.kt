package eu.torvian.chatbot.common.models

import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import eu.torvian.chatbot.common.api.ApiError

/**
 * Represents a discrete update event sent during a streaming chat completion.
 * Used for server-to-client communication to incrementally build the chat message.
 */
@Serializable
sealed class ChatUpdate {
    /**
     * Sent once the user's message has been processed and saved on the server.
     */
    @Serializable
    data class UserMessageUpdate(val message: ChatMessage.UserMessage) : ChatUpdate()

    /**
     * Sent when the assistant's message starts, providing the initial assistant message with empty content.
     * The assistant message contains a temporary ID that will be replaced with the real ID at the end.
     */
    @Serializable
    data class AssistantMessageStart(
        val assistantMessage: ChatMessage.AssistantMessage
    ) : ChatUpdate()

    /**
     * Sent for each new content chunk from the LLM.
     * The client should append `deltaContent` to the existing message identified by `messageId`.
     * The `messageId` here is the client-side temporary ID.
     */
    @Serializable
    data class AssistantMessageDelta(val messageId: Long, val deltaContent: String) : ChatUpdate()

    /**
     * Sent when the assistant's message generation is complete.
     * Provides the temporary ID that was used, the final persisted assistant message with its real ID,
     * and the updated user message (which now has the assistant message as a child).
     */
    @Serializable
    data class AssistantMessageEnd(
        val tempMessageId: Long, // The temporary ID used during streaming
        val finalAssistantMessage: ChatMessage.AssistantMessage, // The final, persisted assistant message with its real ID
        val finalUserMessage: ChatMessage.UserMessage // The updated user message with the assistant message as a child
    ) : ChatUpdate()

    /**
     * Sent if an error occurs during the streaming process.
     */
    @Serializable
    data class ErrorUpdate(val error: ApiError) : ChatUpdate()

    /**
     * Sent as the final signal to indicate the end of the entire stream.
     */
    @Serializable
    data object Done : ChatUpdate()
}
